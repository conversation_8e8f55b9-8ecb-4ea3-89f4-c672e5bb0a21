import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import { I18nValidationExceptionFilter, I18nValidationPipe } from 'nestjs-i18n';
import { initializeTransactionalContext } from 'typeorm-transactional';

import {
  ValidationPipe,
  BadRequestException,
  RequestMethod,
} from '@nestjs/common';
import { HttpFilterException } from './common/filters/http-exception.filter';
import * as cors from 'cors';

async function bootstrap() {
  initializeTransactionalContext();

  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      // whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  app.use(cors({ exposedHeaders: ['Content-Disposition'] }));

  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
    // credentials: true
  });

  // Swagger Setting
  const config = new DocumentBuilder()
    .setTitle('SIT group assignment API')
    .setDescription('API documentation for SIT group assignment application')
    .setVersion('1.0')
    .addBearerAuth() // Add Bearer Auth for JWT
    .addServer('/api')
    .build();

  app.useGlobalPipes(
    new I18nValidationPipe({ whitelist: true, transform: true }),
  );

  app.useGlobalFilters(
    new HttpFilterException(),
    new I18nValidationExceptionFilter({
      detailedErrors: true,
    }),
  );

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document);
  // const document = SwaggerModule.createDocument(app, config);
  // SwaggerModule.setup('api', app, document);

  app.useLogger(new Logger());

  // Only set global prefix for non-ADFS routes
  app.setGlobalPrefix('/api', {
    exclude: [
      { path: 'oauth2/login', method: RequestMethod.GET },
      { path: 'oauth2/callback', method: RequestMethod.GET },
    ],
  });
  

  // Setup WebSocket server
  await app.listen(process.env.APP_PORT || 3000);
  
  Logger.log(
    'Application is running on: http://localhost:' + process.env.APP_PORT,
  );
  Logger.log(
    'WebSocket server is running on: ws://localhost:' + process.env.APP_PORT + '/threads',
  );
}

bootstrap();
