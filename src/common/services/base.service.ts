import { Repository, SelectQueryBuilder } from 'typeorm';
import {
  PaginationResponseDto,
  PaginationOptions,
} from '../dto/pagination.dto';
import {
  getPaginationOptions,
  createPaginationResponse,
} from '../utils/pagination.util';

export abstract class BaseService<T> {
  constructor(protected readonly repository: Repository<T>) {}

  protected async paginateQuery(
    queryBuilder: SelectQueryBuilder<T>,
    page?: number,
    limit?: number,
  ): Promise<PaginationResponseDto<T>> {
    const {
      page: currentPage,
      limit: currentLimit,
      skip,
    } = getPaginationOptions(page, limit);

    // Get total count
    const total = await queryBuilder.getCount();

    // Get paginated data
    const data = await queryBuilder.skip(skip).take(currentLimit).getMany();

    return createPaginationResponse(data, total, currentPage, currentLimit);
  }

  protected getPaginationOptions(
    page?: number,
    limit?: number,
  ): PaginationOptions {
    return getPaginationOptions(page, limit);
  }

  protected createPaginationResponse(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): PaginationResponseDto<T> {
    return createPaginationResponse(data, total, page, limit);
  }
}
