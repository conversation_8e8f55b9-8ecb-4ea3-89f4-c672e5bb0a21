import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  CallH<PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { I18nService } from 'nestjs-i18n';

export interface Response<T> {
  success: boolean;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  constructor(private readonly i18n: I18nService) {}

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    return next.handle().pipe(
      map(async (data) => {
        // If data already has success property, return as is
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // If data has pagination structure, format it properly
        if (
          data &&
          typeof data === 'object' &&
          'data' in data &&
          'pagination' in data
        ) {
          return {
            success: true,
            data: data.data,
            pagination: data.pagination,
          };
        }

        // Handle message objects with i18n
        if (data && typeof data === 'object' && 'message' in data) {
          const translatedMessage = await this.i18n.translate(data.message, {
            args: data.args || {},
          });

          return {
            success: true,
            data: { message: translatedMessage },
          };
        }

        // Default response format
        return {
          success: true,
          data,
        };
      }),
    );
  }
}
