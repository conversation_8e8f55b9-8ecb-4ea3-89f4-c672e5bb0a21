export interface I18nMessage {
  message: string;
  args?: Record<string, any>;
}

export function i18nMessage(
  key: string,
  args?: Record<string, any>,
): I18nMessage {
  return {
    message: key,
    args,
  };
}

// Common message helpers
export const Messages = {
  document: {
    created: () => i18nMessage('common.messages.document.created'),
    updated: () => i18nMessage('common.messages.document.updated'),
    deleted: () => i18nMessage('common.messages.document.deleted'),
    notFound: () => i18nMessage('common.messages.document.notFound'),
    uploaded: () => i18nMessage('common.messages.document.uploaded'),
    uploadFailed: () => i18nMessage('common.messages.document.uploadFailed'),
  },
  user: {
    created: () => i18nMessage('common.messages.user.created'),
    updated: () => i18nMessage('common.messages.user.updated'),
    deleted: () => i18nMessage('common.messages.user.deleted'),
    notFound: () => i18nMessage('common.messages.user.notFound'),
  },
  course: {
    created: () => i18nMessage('common.messages.course.created'),
    updated: () => i18nMessage('common.messages.course.updated'),
    deleted: () => i18nMessage('common.messages.course.deleted'),
    notFound: () => i18nMessage('common.messages.course.notFound'),
  },
  assignment: {
    created: () => i18nMessage('common.messages.assignment.created'),
    updated: () => i18nMessage('common.messages.assignment.updated'),
    deleted: () => i18nMessage('common.messages.assignment.deleted'),
    notFound: () => i18nMessage('common.messages.assignment.notFound'),
  },
};
