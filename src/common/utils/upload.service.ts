import { Injectable, InternalServerErrorException } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectsCommand,
} from '@aws-sdk/client-s3';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { FileConfig } from '../configs/config.type';
import { CustomLogger } from '../logging';

@Injectable()
export class UploadService {
  private s3: S3Client;
  private bucket: string;

  private env: string;
  private region: string;
  private endpoint?: string;

  private readonly loggerMeta: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLogger,
  ) {
    const fileConfig = configService.get<FileConfig>('file');
    const accessKeyId = fileConfig?.accessKeyId ?? '';
    const secretAccessKey = fileConfig?.secretAccessKey ?? '';
    this.region = fileConfig?.awsS3Region ?? '';
    this.bucket = fileConfig?.awsDefaultS3Bucket ?? '';
    this.endpoint = fileConfig?.awsS3Endpoint; // Custom endpoint

    console.log('File Config:', fileConfig);

    if (!accessKeyId || !secretAccessKey || !this.bucket) {
      throw new Error('AWS credentials are not configured');
    }

    if (!this.endpoint) {
      this.s3 = new S3Client({
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
    } else {
      this.s3 = new S3Client({
        endpoint: this.endpoint,
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
        forcePathStyle: true, // enable if use (MinIO, Wasabi)
      });
    }
    this.env = process.env.NODE_ENV || 'development';
    this.loggerMeta = { context: UploadService.name };
  }

  async uploadFile(
    file: Express.Multer.File,
    modelName: string,
  ): Promise<string> {
    const key = `${this.env}/${modelName}/${uuidv4()}${extname(file.originalname)}`;

    console.log('Uploading file with key:', key);
    const r = await this.s3.config.region();
    console.log(r);

    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    try {
      await this.s3.send(command);

      if (this.endpoint) {
        return `${this.endpoint}/${this.bucket}/${key}`;
      }

      return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
    } catch (error) {
      this.logger.error(
        'Failed to upload file',
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      throw new InternalServerErrorException('Failed to upload file');
    }
  }

  async deleteFiles(fileUrls: string[]): Promise<void> {
    let urlPath = '';
    if (this.endpoint) {
      urlPath = `${this.endpoint}/${this.bucket}/`;
    } else {
      urlPath = `https://${this.bucket}.s3.${this.region}.amazonaws.com/`;
    }
    const objectsToDelete = fileUrls.map((url) => {
      // Extract the key from the URL
      const key = url.replace(urlPath, '');
      return { Key: key };
    });

    const deleteParams = {
      Bucket: this.bucket,
      Delete: {
        Objects: objectsToDelete,
      },
    };

    const command = new DeleteObjectsCommand(deleteParams);

    try {
      await this.s3.send(command);
    } catch (error) {
      this.logger.error(
        'Failed to delete files from S3',
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      throw new InternalServerErrorException('Failed to delete files from S3');
    }
  }
}
