import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  Document,
  DocumentStatus,
  DocumentRepositoryType,
} from './entities/document.entity';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { SearchDocumentsDto } from './dto/search-documents.dto';
import { PaginationResponseDto } from '@src/common/dto/pagination.dto';
import { BaseService } from '@src/common/services/base.service';

@Injectable()
export class DocumentsService extends BaseService<Document> {
  constructor(
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
  ) {
    super(documentRepository);
  }

  async create(createDocumentDto: CreateDocumentDto): Promise<Document> {
    const document = this.documentRepository.create({
      ...createDocumentDto,
      tags: createDocumentDto.tags ? createDocumentDto.tags.split(',') : [],
    });
    return this.documentRepository.save(document);
  }

  async findAll(
    searchDto: SearchDocumentsDto,
  ): Promise<PaginationResponseDto<Document>> {
    const query = this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.uploader', 'uploader')
      .leftJoinAndSelect('document.assignmentGroup', 'assignmentGroup');

    if (searchDto.assignmentGroupId) {
      query.andWhere('document.assignmentGroupId = :assignmentGroupId', {
        assignmentGroupId: searchDto.assignmentGroupId,
      });
    }

    if (searchDto.courseId) {
      query.andWhere('document.courseId = :courseId', {
        courseId: searchDto.courseId,
      });
    }

    if (searchDto.uploaderId) {
      query.andWhere('document.uploaderId = :uploaderId', {
        uploaderId: searchDto.uploaderId,
      });
    }

    if (searchDto.status) {
      query.andWhere('document.status = :status', {
        status: searchDto.status,
      });
    }

    if (searchDto.repositoryType) {
      query.andWhere('document.repositoryType = :repositoryType', {
        repositoryType: searchDto.repositoryType,
      });
    }

    if (searchDto.search) {
      query.andWhere(
        '(document.fileName ILIKE :search OR document.description ILIKE :search)',
        { search: `%${searchDto.search}%` },
      );
    }

    return this.paginateQuery(query, searchDto.page, searchDto.limit);
  }

  async findOne(id: string): Promise<Document> {
    const document = await this.documentRepository.findOne({
      where: { id },
      relations: ['uploader', 'assignmentGroup'],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async update(
    id: string,
    updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    const document = await this.findOne(id);

    if (updateDocumentDto.tags) {
      updateDocumentDto.tags = updateDocumentDto.tags.split(',');
    }

    Object.assign(document, updateDocumentDto);
    return this.documentRepository.save(document);
  }

  async remove(id: string): Promise<void> {
    const document = await this.findOne(id);
    await this.documentRepository.remove(document);
  }

  async findByThreadId(threadId: string): Promise<Document[]> {
    return this.documentRepository.find({
      where: { threadId },
      relations: ['uploader'],
      order: { createdAt: 'DESC' },
    });
  }

  async updateStatus(id: string, status: DocumentStatus): Promise<Document> {
    const document = await this.findOne(id);
    document.status = status;
    return this.documentRepository.save(document);
  }
}
