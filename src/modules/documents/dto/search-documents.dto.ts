import { IsOptional, IsUUID, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SearchDto } from '@src/common/dto/search.dto';
import {
  DocumentStatus,
  DocumentRepositoryType,
} from '../entities/document.entity';

export class SearchDocumentsDto extends SearchDto {
  @ApiProperty({ description: 'Assignment group ID', required: false })
  @IsOptional()
  @IsUUID()
  assignmentGroupId?: string;

  @ApiProperty({ description: 'Course ID', required: false })
  @IsOptional()
  @IsUUID()
  courseId?: string;

  @ApiProperty({ description: 'Uploader ID', required: false })
  @IsOptional()
  @IsUUID()
  uploaderId?: string;

  @ApiProperty({
    description: 'Document status',
    enum: DocumentStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiProperty({
    description: 'Repository type',
    enum: DocumentRepositoryType,
    required: false,
  })
  @IsOptional()
  @IsEnum(DocumentRepositoryType)
  repositoryType?: DocumentRepositoryType;
}
