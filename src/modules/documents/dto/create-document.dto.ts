import { IsString, IsOptional, IsUUID, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DocumentRepositoryType } from '../entities/document.entity';

export class CreateDocumentDto {
  @ApiProperty({ description: 'Assignment group ID' })
  @IsUUID()
  assignmentGroupId: string;

  @ApiProperty({ description: 'Course ID' })
  @IsUUID()
  courseId: string;

  @ApiProperty({ description: 'Document description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Comma-separated tags', required: false })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({ description: 'Thread ID', required: false })
  @IsOptional()
  @IsString()
  threadId?: string;

  @ApiProperty({
    description: 'Repository type',
    enum: DocumentRepositoryType,
    default: DocumentRepositoryType.TEMPORARY,
  })
  @IsOptional()
  @IsEnum(DocumentRepositoryType)
  repositoryType?: DocumentRepositoryType;
}
