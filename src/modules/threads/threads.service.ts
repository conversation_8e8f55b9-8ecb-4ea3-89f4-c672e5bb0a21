import { Injectable } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { z } from 'zod';
import mongoose from 'mongoose';
import { Thread, Message } from './entities/thread.entity';
import { CustomLogger } from '@src/common/logging';
import { CustomResponse, HTTP } from '@common/http';


// Types
type JoinThreadRequest = {
  threadId: string;
  userId: string;
};

type SendMessageRequest = {
  threadId: string;
  userId: string;
  message: {
    status: string;
    message: string;
    timestamp: string;
  };
};

// Validation schemas
const joinThreadSchema = z.object({
  threadId: z.string(),
  userId: z.string(),
});

const sendMessageSchema = z.object({
  threadId: z.string(),
  userId: z.string(),
  message: z.object({
    status: z.string(),
    message: z.string(),
    timestamp: z.string(),
  }),
});

@Injectable()
export class ThreadsService extends HTTP {
  private loggerMeta: any;

  constructor(private readonly logger: CustomLogger) {
    super();
    this.loggerMeta = { context: ThreadsService.name };
    void this.ensureConnection();
  }

  private async ensureConnection() {
    if (mongoose.connection.readyState !== (1 as mongoose.ConnectionStates)) {
      const mongoUri =
        process.env.MONGODB_URI || 'mongodb://localhost:27017/sit-be';
      await mongoose.connect(mongoUri).catch((err) => {
        this.logger.error(
          `Failed to connect to MongoDB: ${err}`,
          this.loggerMeta,
        );
      });
      this.logger.log('Connected to MongoDB', this.loggerMeta);
    }
  }

  // API Methods
  async getAllThreads(): Promise<CustomResponse> {
    try {
      await this.ensureConnection();
      const threads = await Thread.find().sort({ updatedAt: -1 });

      this.logger.log(`Fetched ${threads.length} threads`, this.loggerMeta);
      return this.success({
        data: threads,
        total: threads.length,
      });
    } catch (error) {
      this.logger.error(
        `Error fetching threads: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async getThreadById(threadId: string): Promise<CustomResponse> {
    try {
      await this.ensureConnection();
      const thread = await Thread.findById(threadId);

      if (!thread) {
        return this.setHttpRequest(404, 'Thread not found');
      }

      this.logger.log(`Fetched thread: ${threadId}`, this.loggerMeta);
      return this.success({ data: thread });
    } catch (error) {
      this.logger.error(
        `Error fetching thread: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async createThread(threadData: {
    title: string;
    description?: string;
  }): Promise<CustomResponse> {
    try {
      await this.ensureConnection();

      if (!threadData.title) {
        return this.setHttpRequest(400, 'Title is required');
      }

      const thread = new Thread(threadData);
      const savedThread = await thread.save();

      this.logger.log(
        `Created thread: ${String(savedThread._id)}`,
        this.loggerMeta,
      );
      return this.success({ data: savedThread });
    } catch (error) {
      this.logger.error(
        `Error creating thread: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async getThreadMessages(threadId: string): Promise<CustomResponse> {
    try {
      await this.ensureConnection();

      const thread = await Thread.findById(threadId);
      if (!thread) {
        return this.setHttpRequest(404, 'Thread not found');
      }

      const messages = await Message.find({ threadId }).sort({ timestamp: 1 });

      this.logger.log(
        `Fetched ${messages.length} messages for thread: ${threadId}`,
        this.loggerMeta,
      );
      return this.success({ data: messages });
    } catch (error) {
      this.logger.error(
        `Error fetching messages: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async sendMessage(
    threadId: string,
    messageData: { content: string; senderId: string },
  ): Promise<CustomResponse> {
    try {
      await this.ensureConnection();

      const thread = await Thread.findById(threadId);
      if (!thread) {
        return this.setHttpRequest(404, 'Thread not found');
      }

      const message = new Message({
        threadId,
        senderId: messageData.senderId,
        content: messageData.content,
      });
      const savedMessage = await message.save();

      // Update thread's updatedAt
      thread.updatedAt = new Date();
      await thread.save();

      this.logger.log(
        `Sent message: ${String(savedMessage._id)} in thread: ${threadId}`,
        this.loggerMeta,
      );
      return this.success({ data: savedMessage });
    } catch (error) {
      this.logger.error(
        `Error sending message: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  // WebSocket Controllers
  async handleJoinThread(
    io: Server,
    socket: Socket,
    requestDTO: JoinThreadRequest,
  ) {
    try {
      await this.ensureConnection();

      const { threadId, userId } = requestDTO;

      // Check if thread exists
      const thread = await Thread.findById(threadId);
      if (!thread) {
        return {
          success: false,
          error: 'Thread does not exist'
        };
      }

      // Join the thread room
      await socket.join(threadId);

      // Get online users in the thread
      const onlineUsers = this.getOnlineUsersInThread(io, threadId);

      // Notify other users in the thread
      socket.to(threadId).emit('userJoined', {
        userId,
        threadId,
        onlineUsers: onlineUsers.length,
      });

      this.logger.log(
        `User ${userId} joined thread: ${threadId}`,
        this.loggerMeta,
      );

      return {
        success: true,
        data: {
          threadId,
          onlineUsers: onlineUsers.length
        }
      };
    } catch (error) {
      this.logger.error(
        'JoinThread error:',
        error instanceof Error ? error.message : String(error),
      );
      return {
        success: false,
        error: 'Failed to join thread'
      };
    }
  }

  async handleSendMessage(
    io: Server,
    socket: Socket,
    requestDTO: SendMessageRequest,
  ) {
    try {
      await this.ensureConnection();

      const { threadId, userId, message } = requestDTO;

      // Check if thread exists
      const thread = await Thread.findById(threadId);
      if (!thread) {
        return {
          success: false,
          error: 'Thread does not exist'
        };
      }

      // Save message
      await this.sendMessage(threadId, {
        content: message.message,
        senderId: userId,
      });

      // Broadcast message to all users in the thread
      const broadcastMessage = {
        status: 'Received',
        message: message.message,
        timestamp: message.timestamp,
        senderId: userId,
      };

      io.to(threadId).emit('newMessage', broadcastMessage);

      this.logger.log(
        `Message sent in thread: ${threadId} by user: ${userId}`,
        this.loggerMeta,
      );

      return {
        success: true,
        data: {
          message: broadcastMessage
        }
      };
    } catch (error) {
      this.logger.error(
        'SendMessage error:',
        error instanceof Error ? error.message : String(error),
      );
      return {
        success: false,
        error: 'Failed to send message'
      };
    }
  }

  // Advanced methods
  async searchThreads(searchTerm: string): Promise<CustomResponse> {
    try {
      await this.ensureConnection();

      const threads = await Thread.find({
        $or: [
          { title: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } },
        ],
      }).sort({ updatedAt: -1 });

      this.logger.log(
        `Search found ${threads.length} threads for: ${searchTerm}`,
        this.loggerMeta,
      );
      return this.success({ data: threads });
    } catch (error) {
      this.logger.error(
        `Error searching threads: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  async getThreadsWithMessageCount(): Promise<CustomResponse> {
    try {
      await this.ensureConnection();

      const threads = await Thread.aggregate([
        {
          $lookup: {
            from: 'messages',
            localField: '_id',
            foreignField: 'threadId',
            as: 'messages',
          },
        },
        {
          $addFields: {
            messageCount: { $size: '$messages' },
          },
        },
        {
          $project: {
            messages: 0,
          },
        },
        {
          $sort: { updatedAt: -1 },
        },
      ]);

      this.logger.log(
        `Fetched ${threads.length} threads with message count`,
        this.loggerMeta,
      );
      return this.success({ data: threads });
    } catch (error) {
      this.logger.error(
        `Error fetching threads with message count: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );
      return this.setHttpRequest(500, 'Internal server error');
    }
  }

  // Helper method to get online users in a thread
  private getOnlineUsersInThread(io: Server, threadId: string) {
    const room = io.sockets.adapter.rooms.get(threadId);
    return room ? Array.from(room) : [];
  }
}
